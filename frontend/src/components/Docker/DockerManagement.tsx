import React, { useState, useEffect } from 'react';
import {
  Tabs,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Popconfirm,
  Card,
  Row,
  Col,
  Statistic,
  Progress,
} from 'antd';
import { dockerApi, serverApi } from '../../services/api';
import {
  PlusOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { TabPane } = Tabs;
const { Option } = Select;

interface DockerImage {
  repository: string;
  tag: string;
  image_id: string;
  created: string;
  size: string;
}

interface DockerContainer {
  container_id: string;
  name: string;
  image: string;
  status: string;
  ports: string;
  created: string;
}

interface ContainerStats {
  container_id: string;
  cpu_usage_percent: number;
  memory_usage: string;
  memory_limit: string;
  memory_usage_percent: number;
  network_io: string;
  block_io: string;
}

interface GPUStats {
  gpu_id: number;
  name: string;
  utilization: number;
  memory_used: number;
  memory_total: number;
}

const DockerManagement: React.FC = () => {
  const [images, setImages] = useState<DockerImage[]>([]);
  const [containers, setContainers] = useState<DockerContainer[]>([]);
  const [gpuStats, setGpuStats] = useState<GPUStats[]>([]);
  const [servers, setServers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedServer, setSelectedServer] = useState<number>(1);
  const [createContainerVisible, setCreateContainerVisible] = useState(false);
  const [createContainerForm] = Form.useForm();

  // 加载服务器列表
  const loadServers = async () => {
    try {
      const response = await serverApi.getServers();
      console.log('服务器API响应:', response); // 调试日志

      // API响应拦截器已经返回了data，所以response直接就是数据数组
      const data = Array.isArray(response) ? response : [];
      setServers(data);

      if (data.length > 0 && !selectedServer) {
        setSelectedServer(data[0].id);
      }
    } catch (error) {
      console.error('加载服务器列表失败:', error);
    }
  };

  // 加载Docker数据
  const loadDockerData = async () => {
    if (!selectedServer) return;

    try {
      setLoading(true);

      // 并行加载镜像、容器和GPU统计
      const [imagesResponse, containersResponse, gpuResponse] = await Promise.all([
        dockerApi.getImages(selectedServer),
        dockerApi.getContainers(selectedServer),
        dockerApi.getGpuStats(selectedServer)
      ]);

      // API响应拦截器已经返回了data，所以response直接就是数据
      setImages(Array.isArray(imagesResponse) ? imagesResponse : []);
      setContainers(Array.isArray(containersResponse) ? containersResponse : []);
      setGpuStats(Array.isArray(gpuResponse) ? gpuResponse : []);
    } catch (error) {
      console.error('加载Docker数据失败:', error);
      message.error('加载Docker数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadServers();
  }, []);

  useEffect(() => {
    loadDockerData();
  }, [selectedServer]);

  const imageColumns: ColumnsType<DockerImage> = [
    {
      title: '镜像仓库',
      dataIndex: 'repository',
      key: 'repository',
    },
    {
      title: '标签',
      dataIndex: 'tag',
      key: 'tag',
      render: (text) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '镜像ID',
      dataIndex: 'image_id',
      key: 'image_id',
      render: (text) => <code>{text.substring(0, 12)}</code>,
    },
    {
      title: '创建时间',
      dataIndex: 'created',
      key: 'created',
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Popconfirm
            title="确定要删除这个镜像吗？"
            onConfirm={() => handleDeleteImage(record.image_id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const containerColumns: ColumnsType<DockerContainer> = [
    {
      title: '容器名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '镜像',
      dataIndex: 'image',
      key: 'image',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => {
        const isRunning = text.includes('Up');
        return (
          <Tag color={isRunning ? 'green' : 'red'}>
            {isRunning ? '运行中' : '已停止'}
          </Tag>
        );
      },
    },
    {
      title: '端口映射',
      dataIndex: 'ports',
      key: 'ports',
    },
    {
      title: '创建时间',
      dataIndex: 'created',
      key: 'created',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => {
        const isRunning = record.status.includes('Up');
        return (
          <Space size="middle">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewLogs(record.container_id)}
            >
              日志
            </Button>
            <Button
              type="link"
              icon={isRunning ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={() => isRunning ? handleStopContainer(record.container_id) : handleStartContainer(record.container_id)}
            >
              {isRunning ? '停止' : '启动'}
            </Button>
            <Button
              type="link"
              icon={<ReloadOutlined />}
              onClick={() => handleRestartContainer(record.container_id)}
            >
              重启
            </Button>
            <Popconfirm
              title="确定要删除这个容器吗？"
              onConfirm={() => handleDeleteContainer(record.container_id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger icon={<DeleteOutlined />}>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  const handlePullImage = () => {
    let imageName = '';

    Modal.confirm({
      title: '拉取镜像',
      content: (
        <Input
          placeholder="例如: nginx:latest"
          onChange={(e) => imageName = e.target.value}
        />
      ),
      onOk: async () => {
        if (!imageName.trim()) {
          message.error('请输入镜像名称');
          return;
        }

        try {
          await dockerApi.pullImage(selectedServer, imageName);
          message.success('镜像拉取成功');
          loadDockerData(); // 重新加载数据
        } catch (error) {
          message.error('镜像拉取失败');
        }
      },
    });
  };

  const handleDeleteImage = async (imageId: string) => {
    try {
      await dockerApi.deleteImage(selectedServer, imageId);
      message.success('镜像删除成功');
      loadDockerData(); // 重新加载数据
    } catch (error) {
      message.error('镜像删除失败');
    }
  };

  const handleStartContainer = async (containerId: string) => {
    try {
      await dockerApi.startContainer(selectedServer, containerId);
      message.success('容器启动成功');
      loadDockerData(); // 重新加载数据
    } catch (error) {
      message.error('容器启动失败');
    }
  };

  const handleStopContainer = async (containerId: string) => {
    try {
      await dockerApi.stopContainer(selectedServer, containerId);
      message.success('容器停止成功');
      loadDockerData(); // 重新加载数据
    } catch (error) {
      message.error('容器停止失败');
    }
  };

  const handleRestartContainer = async (containerId: string) => {
    try {
      await dockerApi.restartContainer(selectedServer, containerId);
      message.success('容器重启成功');
      loadDockerData(); // 重新加载数据
    } catch (error) {
      message.error('容器重启失败');
    }
  };

  const handleDeleteContainer = async (containerId: string) => {
    try {
      await dockerApi.deleteContainer(selectedServer, containerId);
      message.success('容器删除成功');
      loadDockerData(); // 重新加载数据
    } catch (error) {
      message.error('容器删除失败');
    }
  };

  const handleViewLogs = async (containerId: string) => {
    try {
      const logs = await dockerApi.getContainerLogs(selectedServer, containerId);

      Modal.info({
        title: `容器日志 - ${containerId.substring(0, 12)}`,
        content: (
          <div style={{ maxHeight: 400, overflow: 'auto' }}>
            <pre style={{ fontSize: 12, whiteSpace: 'pre-wrap' }}>
              {(logs as any).logs || '暂无日志'}
            </pre>
          </div>
        ),
        width: 800,
      });
    } catch (error) {
      message.error('获取容器日志失败');
    }
  };

  const handleCreateContainer = () => {
    setCreateContainerVisible(true);
    createContainerForm.resetFields();
  };

  const handleCreateContainerOk = async () => {
    try {
      const values = await createContainerForm.validateFields();

      // 处理端口映射
      const ports = values.ports ? values.ports.split(',').map((p: string) => p.trim()).filter((p: string) => p) : [];

      // 处理环境变量
      const environment = values.environment ? values.environment.split('\n').map((e: string) => e.trim()).filter((e: string) => e) : [];

      // 处理卷挂载
      const volumes = values.volumes ? values.volumes.split('\n').map((v: string) => v.trim()).filter((v: string) => v) : [];

      const config = {
        name: values.name,
        image: values.image,
        ports,
        environment,
        volumes,
        network: values.network,
        cpu_limit: values.cpu_limit,
        memory_limit: values.memory_limit,
        restart_policy: values.restart_policy,
        working_dir: values.working_dir,
        command: values.command
      };

      await dockerApi.createContainer(selectedServer, config);
      message.success('容器创建成功');
      setCreateContainerVisible(false);
      createContainerForm.resetFields();
      loadDockerData(); // 重新加载数据
    } catch (error) {
      console.error('创建容器失败:', error);
      message.error('创建容器失败');
    }
  };

  const runningContainers = containers.filter(c => c.status.includes('Up')).length;
  const totalContainers = containers.length;

  return (
    <div>
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={4}>
            <strong>选择服务器:</strong>
          </Col>
          <Col span={8}>
            <Select
              value={selectedServer}
              onChange={setSelectedServer}
              style={{ width: '100%' }}
              placeholder="请选择服务器"
            >
              {servers?.map(server => (
                <Select.Option key={server.id} value={server.id}>
                  {server.name} ({server.host})
                </Select.Option>
              )) || []}
            </Select>
          </Col>
        </Row>
      </Card>

      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总镜像数"
              value={images.length}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总容器数"
              value={totalContainers}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="运行中容器"
              value={runningContainers}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="容器运行率"
              value={totalContainers > 0 ? Math.round((runningContainers / totalContainers) * 100) : 0}
              suffix="%"
              valueStyle={{ color: runningContainers / totalContainers > 0.8 ? '#52c41a' : '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      <Tabs defaultActiveKey="containers">
        <TabPane tab="容器管理" key="containers">
          <Card
            title="容器列表"
            extra={
              <Space>
                <Button icon={<ReloadOutlined />} onClick={() => setLoading(true)}>
                  刷新
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateContainer}>
                  创建容器
                </Button>
              </Space>
            }
          >
            <Table
              columns={containerColumns}
              dataSource={containers}
              rowKey="container_id"
              loading={loading}
            />
          </Card>
        </TabPane>

        <TabPane tab="镜像管理" key="images">
          <Card
            title="镜像列表"
            extra={
              <Space>
                <Button icon={<ReloadOutlined />} onClick={() => setLoading(true)}>
                  刷新
                </Button>
                <Button type="primary" icon={<DownloadOutlined />} onClick={handlePullImage}>
                  拉取镜像
                </Button>
              </Space>
            }
          >
            <Table
              columns={imageColumns}
              dataSource={images}
              rowKey="image_id"
              loading={loading}
            />
          </Card>
        </TabPane>

        <TabPane tab="GPU监控" key="gpu">
          <Row gutter={16}>
            {gpuStats.map((gpu) => (
              <Col span={12} key={gpu.gpu_id} style={{ marginBottom: 16 }}>
                <Card title={`GPU ${gpu.gpu_id} - ${gpu.name}`}>
                  <Row gutter={16}>
                    <Col span={12}>
                      <div style={{ marginBottom: 16 }}>
                        <div>GPU使用率</div>
                        <Progress
                          percent={gpu.utilization}
                          status={gpu.utilization > 80 ? 'exception' : 'normal'}
                        />
                      </div>
                    </Col>
                    <Col span={12}>
                      <div style={{ marginBottom: 16 }}>
                        <div>显存使用率</div>
                        <Progress
                          percent={Math.round((gpu.memory_used / gpu.memory_total) * 100)}
                          status={gpu.memory_used / gpu.memory_total > 0.8 ? 'exception' : 'normal'}
                        />
                      </div>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Statistic
                        title="已用显存"
                        value={gpu.memory_used}
                        suffix="MB"
                        valueStyle={{ fontSize: 14 }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="总显存"
                        value={gpu.memory_total}
                        suffix="MB"
                        valueStyle={{ fontSize: 14 }}
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>
            ))}
          </Row>
        </TabPane>
      </Tabs>

      {/* 创建容器模态框 */}
      <Modal
        title="创建Docker容器"
        open={createContainerVisible}
        onOk={handleCreateContainerOk}
        onCancel={() => setCreateContainerVisible(false)}
        width={800}
        okText="创建"
        cancelText="取消"
      >
        <Form
          form={createContainerForm}
          layout="vertical"
          initialValues={{
            restart_policy: 'unless-stopped'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="容器名称"
                name="name"
                rules={[{ required: true, message: '请输入容器名称' }]}
              >
                <Input placeholder="例如: my-nginx" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="镜像"
                name="image"
                rules={[{ required: true, message: '请输入镜像名称' }]}
              >
                <Input placeholder="例如: nginx:latest" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="端口映射"
                name="ports"
                tooltip="格式: 宿主机端口:容器端口，多个端口用逗号分隔"
              >
                <Input placeholder="例如: 80:80, 443:443" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="网络模式"
                name="network"
              >
                <Select placeholder="选择网络模式">
                  <Option value="bridge">bridge</Option>
                  <Option value="host">host</Option>
                  <Option value="none">none</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="CPU限制"
                name="cpu_limit"
                tooltip="CPU核心数限制，例如: 1.5"
              >
                <Input placeholder="例如: 1.0" type="number" step="0.1" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="内存限制"
                name="memory_limit"
                tooltip="内存限制，例如: 512m, 1g"
              >
                <Input placeholder="例如: 512m" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="重启策略"
            name="restart_policy"
          >
            <Select>
              <Option value="no">no</Option>
              <Option value="always">always</Option>
              <Option value="unless-stopped">unless-stopped</Option>
              <Option value="on-failure">on-failure</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="工作目录"
            name="working_dir"
          >
            <Input placeholder="例如: /app" />
          </Form.Item>

          <Form.Item
            label="启动命令"
            name="command"
          >
            <Input placeholder="例如: nginx -g 'daemon off;'" />
          </Form.Item>

          <Form.Item
            label="环境变量"
            name="environment"
            tooltip="每行一个环境变量，格式: KEY=VALUE"
          >
            <Input.TextArea
              rows={3}
              placeholder="例如:&#10;NODE_ENV=production&#10;PORT=3000"
            />
          </Form.Item>

          <Form.Item
            label="卷挂载"
            name="volumes"
            tooltip="每行一个挂载，格式: 宿主机路径:容器路径"
          >
            <Input.TextArea
              rows={3}
              placeholder="例如:&#10;/host/data:/container/data&#10;/host/logs:/container/logs"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DockerManagement;
